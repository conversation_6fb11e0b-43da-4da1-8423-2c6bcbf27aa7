package wlmwwx.duckdns.org.hearingaid.ui.navigation

import android.Manifest
import android.content.Context
import android.content.pm.PackageManager
import android.media.AudioFormat
import android.media.AudioManager
import android.media.AudioRecord
import android.media.AudioTrack
import android.media.MediaRecorder
import android.util.Log
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Button
import androidx.compose.material3.Slider
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import androidx.core.content.ContextCompat
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

private const val TAG = "HearingAidScreen"
private const val SAMPLE_RATE = 44100
private const val CHANNEL_IN_CONFIG = AudioFormat.CHANNEL_IN_MONO
private const val CHANNEL_OUT_CONFIG = AudioFormat.CHANNEL_OUT_MONO
private const val AUDIO_FORMAT = AudioFormat.ENCODING_PCM_16BIT

@Composable
fun HearingAidScreen() {
    var isHearingAidActive by remember { mutableStateOf(false) }
    var volumeLevel by remember { mutableStateOf(0.5f) } // 0.0f to 1.0f, UI maps to 0-100%

    val context = LocalContext.current
    val scope = rememberCoroutineScope()

    var audioRecord by remember { mutableStateOf<AudioRecord?>(null) }
    var audioTrack by remember { mutableStateOf<AudioTrack?>(null) }
    var audioJob by remember { mutableStateOf<Job?>(null) }

    DisposableEffect(isHearingAidActive, volumeLevel) {
        if (isHearingAidActive) {
            if (ContextCompat.checkSelfPermission(context, Manifest.permission.RECORD_AUDIO) != PackageManager.PERMISSION_GRANTED) {
                Log.w(TAG, "RECORD_AUDIO permission not granted. Cannot start hearing aid.")
                // Update isHearingAidActive back to false because we can't proceed
                // This needs to be done carefully to avoid re-triggering DisposableEffect infinitely if permission is denied
                // One way is to launch a new coroutine to change the state after current composition
                scope.launch { isHearingAidActive = false }
                onDispose { /* Nothing to dispose if not started */ }
                return@DisposableEffect
            }

            audioJob = scope.launch(Dispatchers.IO) {
                try {
                    val minBufferSize = AudioRecord.getMinBufferSize(SAMPLE_RATE, CHANNEL_IN_CONFIG, AUDIO_FORMAT)
                    val playerMinBufferSize = AudioTrack.getMinBufferSize(SAMPLE_RATE, CHANNEL_OUT_CONFIG, AUDIO_FORMAT)

                    if (minBufferSize == AudioRecord.ERROR_BAD_VALUE || playerMinBufferSize == AudioTrack.ERROR_BAD_VALUE) {
                        Log.e(TAG, "Invalid audio parameters. Cannot initialize AudioRecord/AudioTrack.")
                        withContext(Dispatchers.Main) { isHearingAidActive = false }
                        return@launch
                    }

                    val bufferSize = minBufferSize.coerceAtLeast(playerMinBufferSize)


                    val currentAudioRecord = AudioRecord(
                        MediaRecorder.AudioSource.MIC,
                        SAMPLE_RATE,
                        CHANNEL_IN_CONFIG,
                        AUDIO_FORMAT,
                        bufferSize
                    )
                    audioRecord = currentAudioRecord

                    val currentAudioTrack = AudioTrack(
                        AudioManager.STREAM_MUSIC,
                        SAMPLE_RATE,
                        CHANNEL_OUT_CONFIG,
                        AUDIO_FORMAT,
                        bufferSize,
                        AudioTrack.MODE_STREAM
                    )
                    audioTrack = currentAudioTrack

                    if (currentAudioRecord.state != AudioRecord.STATE_INITIALIZED || currentAudioTrack.state != AudioTrack.STATE_INITIALIZED) {
                         Log.e(TAG, "AudioRecord or AudioTrack failed to initialize.")
                         withContext(Dispatchers.Main) { isHearingAidActive = false }
                         return@launch
                    }

                    currentAudioRecord.startRecording()
                    currentAudioTrack.play()

                    val audioBuffer = ShortArray(bufferSize / 2) // Each short is 2 bytes

                    while (isActive && isHearingAidActive) { // Check isHearingAidActive as well, as volumeLevel change can re-trigger keeping isActive true
                        val bytesRead = currentAudioRecord.read(audioBuffer, 0, audioBuffer.size)
                        if (bytesRead > 0) {
                            // Apply amplification
                            for (i in 0 until bytesRead) {
                                val amplifiedSample = audioBuffer[i] * volumeLevel * 2.0f // Multiply by 2 for potentially more amplification
                                // Clamp to Short min/max values
                                audioBuffer[i] = amplifiedSample.coerceIn(Short.MIN_VALUE.toFloat(), Short.MAX_VALUE.toFloat()).toInt().toShort()
                            }
                            currentAudioTrack.write(audioBuffer, 0, bytesRead)
                        }
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "Audio processing error: ${e.message}", e)
                    withContext(Dispatchers.Main) {
                        isHearingAidActive = false // Stop on error
                    }
                } finally {
                    Log.d(TAG, "Stopping and releasing audio resources in coroutine finally block")
                    audioRecord?.stop()
                    audioRecord?.release()
                    audioRecord = null
                    audioTrack?.stop()
                    audioTrack?.release()
                    audioTrack = null
                }
            }
        }

        onDispose {
            Log.d(TAG, "DisposableEffect onDispose: Cleaning up. isHearingAidActive: $isHearingAidActive")
            audioJob?.cancel() // Cancel the coroutine
            audioJob = null

            // Release resources if they are still held (e.g. coroutine cancelled before finally block)
            if (audioRecord?.recordingState == AudioRecord.RECORDSTATE_RECORDING) {
                audioRecord?.stop()
            }
            audioRecord?.release()
            audioRecord = null

            if (audioTrack?.playState == AudioTrack.PLAYSTATE_PLAYING) {
                audioTrack?.stop()
            }
            audioTrack?.release()
            audioTrack = null
        }
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.Center,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(text = if (isHearingAidActive) "Hearing Aid: ON" else "Hearing Aid: OFF")
        Spacer(modifier = Modifier.height(16.dp))
        Button(onClick = {
            isHearingAidActive = !isHearingAidActive
            if (!isHearingAidActive) { // If stopping, ensure job is cancelled
                audioJob?.cancel()
            }
        }) {
            Text(text = if (isHearingAidActive) "Stop" else "Start")
        }
        Spacer(modifier = Modifier.height(32.dp))
        Text(text = "Volume: ${ (volumeLevel * 100).toInt() }%")
        Slider(
            value = volumeLevel,
            onValueChange = { newVolume ->
                volumeLevel = newVolume
                // No need to explicitly restart the job here, DisposableEffect handles it due to volumeLevel key change
            },
            valueRange = 0f..1f,
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 24.dp)
        )
    }
}

import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.sizeIn
import androidx.compose.foundation.layout.wrapContentSize
import kotlinx.coroutines.delay
import kotlin.math.PI
import kotlin.math.sin

private const val TEST_SAMPLE_RATE = 44100
private const val TEST_AUDIO_FORMAT = AudioFormat.ENCODING_PCM_16BIT
private const val TEST_CHANNEL_CONFIG = AudioFormat.CHANNEL_OUT_MONO
private const val TAG_TEST = "HearingTestScreen"


@Composable
fun HearingTestScreen() {
    val scope = rememberCoroutineScope()
    var selectedFrequency by remember { mutableStateOf<Int?>(null) }
    var isPlayingTone by remember { mutableStateOf(false) }
    // Store results as frequency to boolean (true if heard)
    var testResults by remember { mutableStateOf<Map<Int, Boolean>>(emptyMap()) }
    var audioTrackPlayer by remember { mutableStateOf<AudioTrack?>(null) }
    var currentVolume by remember { mutableStateOf(0.5f) } // Volume for tone playback
    var toneJob by remember { mutableStateOf<Job?>(null) }


    val frequencies = listOf(250, 500, 1000, 2000, 4000, 6000, 8000) // Common audiogram frequencies

    fun stopTone() {
        toneJob?.cancel()
        toneJob = null
        audioTrackPlayer?.apply {
            if (playState == AudioTrack.PLAYSTATE_PLAYING) {
                try {
                    stop()
                } catch (e: IllegalStateException) {
                    Log.e(TAG_TEST, "Error stopping AudioTrack: ${e.message}")
                }
            }
            release()
        }
        audioTrackPlayer = null
        isPlayingTone = false
    }

    fun playTone(frequency: Int, volume: Float, onFinished: () -> Unit) {
        if (isPlayingTone) { // Stop previous tone if any
            stopTone()
        }
        isPlayingTone = true
        toneJob = scope.launch(Dispatchers.IO) {
            try {
                val duration = 1 // seconds
                val numSamples = TEST_SAMPLE_RATE * duration
                val buffer = ShortArray(numSamples)
                val angularFrequency = 2.0 * PI * frequency / TEST_SAMPLE_RATE

                for (i in 0 until numSamples) {
                    val sample = sin(angularFrequency * i) // Sin wave -1.0 to 1.0
                    val scaledSample = (sample * volume * Short.MAX_VALUE).toInt()
                    buffer[i] = scaledSample.coerceIn(Short.MIN_VALUE.toInt(), Short.MAX_VALUE.toInt()).toShort()
                }

                val currentAudioTrack = AudioTrack(
                    AudioManager.STREAM_MUSIC,
                    TEST_SAMPLE_RATE,
                    TEST_CHANNEL_CONFIG,
                    TEST_AUDIO_FORMAT,
                    buffer.size * 2, // size in bytes
                    AudioTrack.MODE_STATIC
                )
                audioTrackPlayer = currentAudioTrack

                if (currentAudioTrack.state != AudioTrack.STATE_INITIALIZED) {
                    Log.e(TAG_TEST, "AudioTrack (static) failed to initialize for tone.")
                    isPlayingTone = false // ensure state is updated
                    onFinished()
                    return@launch
                }

                currentAudioTrack.write(buffer, 0, buffer.size)
                currentAudioTrack.play()

                // Wait for playback to complete (approximately)
                // AudioTrack.getPlaybackHeadPosition() could be used for more accuracy with MODE_STREAM
                delay(duration * 1000L) // Wait for 1 second

            } catch (e: Exception) {
                Log.e(TAG_TEST, "Error playing tone: ${e.message}", e)
            } finally {
                withContext(Dispatchers.Main) {
                    stopTone() // Ensure cleanup happens on main thread if coroutine ends/cancelled
                    onFinished()
                }
            }
        }
    }

    DisposableEffect(Unit) {
        onDispose {
            Log.d(TAG_TEST, "Disposing HearingTestScreen, stopping tone.")
            stopTone()
        }
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(10.dp)
    ) {
        Text("Select Frequency (Hz):")
        Row(
            horizontalArrangement = Arrangement.spacedBy(8.dp),
            modifier = Modifier.wrapContentSize()
        ) {
            frequencies.forEach { freq ->
                Button(
                    onClick = {
                        if (!isPlayingTone) selectedFrequency = freq
                              },
                    enabled = !isPlayingTone,
                    modifier = Modifier.sizeIn(minHeight = 48.dp)
                ) {
                    Text("$freq")
                }
            }
        }

        selectedFrequency?.let {
            Text("Selected: $it Hz", style = androidx.compose.material3.MaterialTheme.typography.titleMedium)
        }

        Spacer(modifier = Modifier.height(10.dp))
        Text("Volume: ${(currentVolume * 100).toInt()}%")
        Slider(
            value = currentVolume,
            onValueChange = { if (!isPlayingTone) currentVolume = it },
            valueRange = 0f..1f,
            modifier = Modifier.fillMaxWidth(0.8f),
            enabled = !isPlayingTone
        )

        Spacer(modifier = Modifier.height(10.dp))
        Button(
            onClick = {
                if (isPlayingTone) {
                    stopTone()
                } else {
                    selectedFrequency?.let { freq ->
                        playTone(freq, currentVolume) {
                            // isPlayingTone is already set to false by stopTone
                        }
                    }
                }
            },
            enabled = selectedFrequency != null
        ) {
            Text(if (isPlayingTone) "Stop Tone" else "Play Tone")
        }

        Button(
            onClick = {
                selectedFrequency?.let { freq ->
                    testResults = testResults + (freq to true) // Mark as heard
                    stopTone() // Stop tone after marking
                    // Optionally, auto-select next frequency or clear selection
                    // selectedFrequency = null
                }
            },
            enabled = isPlayingTone
        ) {
            Text("I Heard It")
        }

        // Optional: Button to mark "Did Not Hear" if the tone stops and user didn't press "I Heard It"
        // This might be useful if a timeout is implemented for "I Heard It" button
        Button(
            onClick = {
                selectedFrequency?.let { freq ->
                     if (!isPlayingTone) { // Only allow if tone is not playing (e.g. it finished)
                        testResults = testResults + (freq to false)
                        // selectedFrequency = null
                     }
                }
            },
            // Enable if a frequency was selected, and it's not currently playing (it finished or was stopped)
            // And it hasn't been marked as "heard" yet for the current selection if selection persists
            enabled = selectedFrequency != null && !isPlayingTone && testResults[selectedFrequency] == null
        ) {
            Text("Did Not Hear")
        }


        Spacer(modifier = Modifier.height(20.dp))
        Text("Test Results:", style = androidx.compose.material3.MaterialTheme.typography.titleMedium)
        testResults.toSortedMap().forEach { (freq, heard) ->
            Text("$freq Hz: ${if (heard) "Heard" else "Not Heard"}")
        }
    }
}

import androidx.compose.foundation.clickable
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.KeyboardArrowRight
import androidx.compose.material3.Divider
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.RadioButton

@Composable
private fun SettingItemRow(title: String, onClick: () -> Unit) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .fillMaxWidth()
            .sizeIn(minHeight = 48.dp)
            .clickable(onClick = onClick)
            .padding(horizontal = 16.dp, vertical = 12.dp), // Adjusted padding
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = title,
            style = MaterialTheme.typography.bodyLarge,
            modifier = Modifier.weight(1f)
        )
        Icon(
            imageVector = Icons.Filled.KeyboardArrowRight,
            contentDescription = title // Content description added
        )
    }
}

@Composable
fun MyScreen() {
    var selectedThemeOption by remember { mutableStateOf("System Default") }
    val themeOptions = listOf("Light", "Dark", "System Default")

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(top = 16.dp) // Add some padding at the top
    ) {
        Text(
            text = "Settings",
            style = MaterialTheme.typography.headlineMedium,
            modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp)
        )

        Text(
            text = "Theme",
            style = MaterialTheme.typography.titleMedium,
            modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp)
        )
        themeOptions.forEach { option ->
            Row(
                Modifier
                    .fillMaxWidth()
                    .sizeIn(minHeight = 48.dp) // Ensure min touch target size
                    .clickable { selectedThemeOption = option }
                    .padding(horizontal = 16.dp, vertical = 4.dp), // Padding inside the clickable area
                verticalAlignment = Alignment.CenterVertically
            ) {
                RadioButton(
                    selected = (selectedThemeOption == option),
                    onClick = { selectedThemeOption = option }
                )
                Text(
                    text = option,
                    style = MaterialTheme.typography.bodyLarge,
                    modifier = Modifier.padding(start = 8.dp)
                )
            }
        }

        Spacer(modifier = Modifier.height(16.dp))
        Divider(modifier = Modifier.padding(horizontal = 16.dp))
        Spacer(modifier = Modifier.height(8.dp))

        SettingItemRow(title = "User Profile") {
            Log.d("MyScreen", "User Profile clicked")
            // TODO: Navigate to User Profile screen
        }
        SettingItemRow(title = "Help & Feedback") {
            Log.d("MyScreen", "Help & Feedback clicked")
            // TODO: Navigate to Help & Feedback screen
        }
        SettingItemRow(title = "Privacy Policy") {
            Log.d("MyScreen", "Privacy Policy clicked")
            // TODO: Navigate to Privacy Policy screen
        }
        SettingItemRow(title = "About") {
            Log.d("MyScreen", "About clicked")
            // TODO: Navigate to About screen
        }
    }
}
